import { useParams } from 'next/navigation';
import { useMemo } from 'react';

// Import all translation files
import frCommon from '../../locales/fr/common.json';
import enCommon from '../../locales/en/common.json';
import frNavigation from '../../locales/fr/navigation.json';
import enNavigation from '../../locales/en/navigation.json';
import frFooter from '../../locales/fr/footer.json';
import enFooter from '../../locales/en/footer.json';
import frPages from '../../locales/fr/pages.json';
import enPages from '../../locales/en/pages.json';
import frLegal from '../../locales/fr/legal.json';
import enLegal from '../../locales/en/legal.json';
import frProjects from '../../locales/fr/projects.json';
import enProjects from '../../locales/en/projects.json';

const translations = {
  fr: {
    common: frCommon,
    navigation: frNavigation,
    footer: frFooter,
    pages: frPages,
    legal: frLegal,
    projects: frProjects,
  },
  en: {
    common: enCommon,
    navigation: enNavigation,
    footer: enFooter,
    pages: enPages,
    legal: enLegal,
    projects: enProjects,
  },
};

export function useTranslation(namespace = 'common') {
  const params = useParams();
  const locale = params?.locale || 'fr';

  const t = useMemo(() => {
    return (key, variables = {}) => {
      const keys = key.split('.');
      let value = translations[locale][namespace];

      for (const k of keys) {
        if (value && typeof value === 'object') {
          value = value[k];
        } else {
          break;
        }
      }

      if (typeof value !== 'string') {
        console.warn(`Translation key "${key}" not found in namespace "${namespace}" for locale "${locale}"`);
        return key;
      }

      // Replace variables in the translation
      return value.replace(/\{(\w+)\}/g, (match, variable) => {
        return variables[variable] || match;
      });
    };
  }, [locale, namespace]);

  return { t, locale };
}

// Helper function to get translation without hook (for use in getStaticProps, etc.)
export function getTranslation(locale = 'fr', namespace = 'common') {
  return (key, variables = {}) => {
    const keys = key.split('.');
    let value = translations[locale][namespace];

    for (const k of keys) {
      if (value && typeof value === 'object') {
        value = value[k];
      } else {
        break;
      }
    }

    if (typeof value !== 'string') {
      console.warn(`Translation key "${key}" not found in namespace "${namespace}" for locale "${locale}"`);
      return key;
    }

    // Replace variables in the translation
    return value.replace(/\{(\w+)\}/g, (match, variable) => {
      return variables[variable] || match;
    });
  };
}
