.basicCenterCTA {
  position: relative;
  margin-top: var(--section-padding);
  margin-bottom: var(--section-padding);
  background-color: #19271B;

  // le pseudo-élément est désormais en z-index 0
  &::before {
    content: "";
    position: absolute;
    top: -89px;
    left: 0;
    width: 100%;
    height: 90px;
    background-color: #19271B;
    clip-path: polygon(0% 100%, 4% 0%, 99% 0%, 100% 20%, 100% 100%);
    z-index: 0;
  }

  .container {
    position: relative;
    z-index: 1; // le contenu se place au-dessus du pseudo-élément
    padding-bottom: calc(var(--section-padding) / 2);

    h2 {
      color: #fff;
    }
    p {
      color: #C8CEC9;
    }
  }

  .text {
    margin-bottom: calc(var(--gap-padding) * 2);
  }

  @media (min-width: 768px) {
    text-align: center;

    &::before {
      top: -119px;
      height: 120px;
      clip-path: polygon(0% 100%, 5% 0%, 98% 0%, 100% 25%, 100% 100%);
    }
  }

  @media (min-width: 1024px) {
    &::before {
      top: -139px;
      height: 140px;
      clip-path: polygon(0% 100%, 4% 0%, 99% 0%, 100% 20%, 100% 100%);
    }
  }
}
