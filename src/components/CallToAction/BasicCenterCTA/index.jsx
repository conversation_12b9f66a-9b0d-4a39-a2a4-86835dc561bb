// components/TitleWithText.js
import React from "react";
import styles from "./style.module.scss";
import Rounded from "../../../common/RoundedButton";
import { useTranslation } from "@/hooks/useTranslation";
import { useParams } from "next/navigation";

export default function BasicCenterCTA() {
  const { t } = useTranslation('common');
  const params = useParams();
  const locale = params?.locale || 'fr';

  return (
    <div className={styles.basicCenterCTA}>
      <div className={`container small ${styles.container}`}>
        <h2 className={styles.title}>
          {t('cta.general_questions_title')}
        </h2>
        <p className={styles.text}>
          {t('cta.general_questions_text')}
        </p>
        <Rounded href={`/${locale}/questions-frequentes`}>
          <p>{t('cta.view_faq')}</p>
        </Rounded>
      </div>
    </div>
  );
}
