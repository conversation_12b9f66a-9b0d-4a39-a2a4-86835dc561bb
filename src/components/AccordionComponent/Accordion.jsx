import React, { useState, useRef } from "react";
import styles from "./style.module.scss";

// composant parent qui gère l'élément actif
export function Accordion({ children }) {
  const [activeIndex, setActiveIndex] = useState(null);

  function handleClick(index) {
    setActiveIndex((prev) => (prev === index ? null : index));
  }

  return (
    <div className="container">
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) return null;
        return React.cloneElement(child, {
          isOpen: activeIndex === index,
          onClick: () => handleClick(index)
        });
      })}
    </div>
  );
}

// composant enfant pour un bloc question/réponse
export function AccordionItem({ title, children, isOpen, onClick, ...rest }) {
  const contentRef = useRef(null);

  return (
    <div className={styles.wrapper}>
      <button
        {...rest}
        onClick={onClick}
        className={`${styles.questionContainer} ${
          isOpen ? styles.questionContainerActive : ""
        }`}
      >
        <p className={styles.questionContent}>{title}</p>
        <span
          className="material-symbols-outlined"
          style={{
            transform: isOpen ? "rotate(45deg)" : "rotate(0deg)",
            transition: "transform 0.3s ease"
          }}
        >
          add
        </span>
      </button>

      <div
        className={styles.answerContainer}
        ref={contentRef}
        style={{
          height: isOpen && contentRef.current
            ? contentRef.current.scrollHeight
            : 0
        }}
      >
        <div className={styles.answerContent}>{children}</div>
      </div>
    </div>
  );
}
