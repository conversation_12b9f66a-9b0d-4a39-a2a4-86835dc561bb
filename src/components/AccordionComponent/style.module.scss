.container {
  /* styles globaux si nécessaire */
}

.wrapper {
  border-bottom: 1px solid rgb(201, 201, 201);
  overflow: hidden;
  max-width: 850px;
}

/* bouton question */
.questionContainer {
  width: 100%;
  text-align: left;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 20px;
  font-family: inherit;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #19271B; // ou la couleur que tu préfères
  -webkit-tap-highlight-color: transparent;
}

/* texte de la question */
.questionContent {}

/* conteneur de la réponse */
.answerContainer {
  padding: 0 1rem;
  padding-top: 0;
  transition: height 0.7s ease-in-out;
  overflow: hidden;
}

/* texte de la réponse */
.answerContent {
  padding: 1rem 0;
  padding-top: 0;
  margin-top: 0;
}

/* sur mobile, ajuster l'alignement de l'icône */
@media screen and (max-width: 768px) {
  .material-symbols-outlined {
    align-self: flex-start;
    margin-top: 8px;
  }
}

.material-symbols-outlined {
  color: #19271B; // pour forcer la couleur de l'icône
}
