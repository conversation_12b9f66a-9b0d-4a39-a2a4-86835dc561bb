import TextReveal from '@/components/TextReveal'
import styles from './style.module.scss'
import { useTranslation } from '@/hooks/useTranslation'

export default function Hero({
  title,
  subtitle,
  locale = 'fr',
  containerClass = 'container'
}) {
  const { t } = useTranslation('pages');

  const defaultTitle = t('home.hero_title');
  const defaultSubtitle = t('home.hero_subtitle');

  return (
    <main className={`${containerClass} default-hero`}>
      <TextReveal as="h1" className={styles.heading}>
        {title || defaultTitle}
      </TextReveal>
      {(subtitle || defaultSubtitle) && (
        <TextReveal
          as="p"
          className="text-big"
          stagger={0.15}
          delayStart={0.5}
        >
          {subtitle || defaultSubtitle}
        </TextReveal>
      )}
    </main>
  )
}


