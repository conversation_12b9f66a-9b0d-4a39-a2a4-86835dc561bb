    import styles from './style.module.scss';
    import { useInView, motion } from 'framer-motion';
    import { useRef } from 'react';
    import { slideUp, opacity } from './animation';
    import Rounded from '../../common/RoundedButton';
    import { useLenisParallax } from '@/hooks/useLenisParallax';

    export default function Description({ 
        descriptionTitle = "Helping brands to stand out in the digital era. Together we will set the new status quo. No nonsense, always on the cutting edge.", 
        descriptionText = "The combination of my passion for design, code & interaction positions me in a unique place in the web design world.", 
        showButton = true, 
        buttonText = "About me" 
    }) {

        const description = useRef(null);
        const isInView = useInView(description);
        const parallaxRef = useLenisParallax(0.1);

        return (
            <div ref={description} className={`${styles.description} container medium`}>
                <div className={styles.body}>
                    <h2>
                        {
                            descriptionTitle.split(" ").map((word, index) => {
                                return (
                                    <span key={index} className={styles.mask}>
                                        <motion.span
                                            variants={slideUp}
                                            custom={index}
                                            animate={isInView ? "open" : "closed"}
                                        >
                                            {word}
                                        </motion.span>
                                    </span>
                                );
                            })
                        }
                    </h2>
                    <motion.div
                        variants={opacity}
                        animate={isInView ? "open" : "closed"}
                        className={`${styles.descriptionText} text-big`}
                    >
                        {typeof descriptionText === 'string' ? (
                            <p>{descriptionText}</p>
                        ) : (
                            descriptionText
                        )}
                    </motion.div>
                    {showButton && (
                        <div ref={parallaxRef}>
                            <Rounded className={styles.button}>
                                <p>{buttonText}</p>
                            </Rounded>
                        </div>
                    )}
                </div>
            </div>
        );
    }
