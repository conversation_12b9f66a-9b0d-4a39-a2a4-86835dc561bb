// app/[locale]/not-found.jsx

"use client";
  
import '../globals.scss';
import BasicCenterCTA from '@/components/CallToAction/BasicCenterCTA';
import Rounded from '@/common/RoundedButton';
import { useTranslation } from '@/hooks/useTranslation';
import { useParams } from 'next/navigation';

export default function NotFound() {
  const { t } = useTranslation('pages');
  const params = useParams();
  const locale = params?.locale || 'fr';

  return (
    <div className="container">
      <h1>{t('not_found.heading')}</h1>
      <p>{t('not_found.message')}</p>

      <Rounded href={`/${locale}`}>
        <p>{t('not_found.back_home')}</p>
      </Rounded>
    </div>
  );
}
