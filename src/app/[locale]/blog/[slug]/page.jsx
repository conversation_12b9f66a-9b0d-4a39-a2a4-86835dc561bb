import { allPosts } from 'contentlayer/generated';
import { notFound } from 'next/navigation';
import { format as fmt } from 'date-fns';
import { fr, enUS } from 'date-fns/locale';
import Hero2 from '@/components/Heros/Hero2';
import Breadcrumb from '@/components/Breadcrumb';
import { MDXComponent } from '@/components/MDXComponent';
import styles from './page.module.scss';
import { getTranslation } from '@/hooks/useTranslation';

export const generateStaticParams = () => {
  const posts = allPosts.filter((p) => p.published);

  return posts.map((p) => ({
    locale: p.locale,
    slug: p.slug,
  }));
};

export function generateMetadata({ params }) {
  const post = allPosts.find((p) => p.slug === params.slug && p.locale === params.locale);
  if (!post || !post.published) notFound();

  return {
    title: post.title,
    description: post.excerpt,
    keywords: post.tags,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      images: post.cover ? [{ url: post.cover }] : [{ url: "https://kapreon.com/og-default.jpg" }],
      url: `https://kapreon.com/${params.locale}/blog/${params.slug}`,
      locale: params.locale === 'fr' ? 'fr_CA' : 'en_CA',
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.excerpt,
      images: post.cover ? [post.cover] : ["https://kapreon.com/og-default.jpg"],
    },
  };
}

export default function BlogPost({ params }) {
  const locale = params.locale || 'fr';
  const post = allPosts.find((p) => p.slug === params.slug && p.locale === locale);

  if (!post || !post.published) notFound();

  const dateLocale = locale === 'fr' ? fr : enUS;
  const t = getTranslation(locale, 'pages');

  const crumbs = [
    { label: t('blog.hero_title'), href: `/${locale}/blog` },
    { label: post.title, href: `/${locale}/blog/${post.slug}` }
  ];

  return (
    <main className="prose mx-auto py-12 section">
      <Breadcrumb items={crumbs} className={styles.breadcrumb} />

      <Hero2
        title={post.title}
        subtitle={post.category ?? t('blog.hero_title')}
        subtitleRight={fmt(post.updated || post.date, 'PPP', { locale: dateLocale })}
        excerpt={post.excerpt}
        imgSrc={post.cover}
        imgAlt={post.title}
      />

      <div className={`${styles.blogContent} container small center`}>
        <MDXComponent code={post.body.code} />
      </div>
    </main>
  );
}
