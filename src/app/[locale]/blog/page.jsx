import { getTranslation } from "@/hooks/useTranslation";
import BlogClient from './BlogClient';

export async function generateMetadata({ params }) {
  const locale = params.locale || 'fr';
  const t = getTranslation(locale, 'pages');

  return {
    title: t('blog.title'),
    description: t('blog.description'),
    openGraph: {
      title: t('blog.title'),
      description: t('blog.description'),
      images: [{ url: "https://kapreon.com/og-default.jpg" }],
      url: `https://kapreon.com/${locale}/blog`,
      locale: locale === 'fr' ? 'fr_CA' : 'en_CA',
    },
    twitter: {
      card: "summary_large_image",
      title: t('blog.title'),
      description: t('blog.description'),
      images: ["https://kapreon.com/og-default.jpg"],
    },
  };
}

export default function BlogPage({ params }) {
  return <BlogClient params={params} />;
}
