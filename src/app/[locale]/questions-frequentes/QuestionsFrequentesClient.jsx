"use client";

import Hero from '@/components/Heros/Hero';
import { Accordion, AccordionItem } from '@/components/AccordionComponent/Accordion';
import styles from "./page.module.scss";
import { useTranslation } from "@/hooks/useTranslation";

export default function QuestionsFrequentesClient({ params }) {
  const { t } = useTranslation('pages');
  const locale = params.locale || 'fr';

  const defaultContent = (
    <p>
      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut feugiat interdum dignissim.
    </p>
  );
  
  return (
    <div className={styles.main}>
      <Hero title={t('faq.hero_title')} locale={locale} />

      <div className={styles.accordionBlock}>
        <h2 className='container'>{t('faq.identity_section')}</h2>
        
        <Accordion>
          <AccordionItem title="Question 1">
            {defaultContent}
          </AccordionItem>
          <AccordionItem title="Question 2">
            {defaultContent}
          </AccordionItem>
          <AccordionItem title="Question 3">
            {defaultContent}
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
}
