import { getTranslation } from "@/hooks/useTranslation";
import QuestionsFrequentesClient from './QuestionsFrequentesClient';

export async function generateMetadata({ params }) {
  const locale = params.locale || 'fr';
  const t = getTranslation(locale, 'pages');

  return {
    title: t('faq.title'),
    description: t('faq.description'),
    openGraph: {
      title: t('faq.title'),
      description: t('faq.description'),
      images: [{ url: "https://kapreon.com/og-default.jpg" }],
      url: `https://kapreon.com/${locale}/questions-frequentes`,
      locale: locale === 'fr' ? 'fr_CA' : 'en_CA',
    },
    twitter: {
      card: "summary_large_image",
      title: t('faq.title'),
      description: t('faq.description'),
      images: ["https://kapreon.com/og-default.jpg"],
    },
  };
}

export default function QuestionsFrequentes({ params }) {
  return <QuestionsFrequentesClient params={params} />;
}
