import { getTranslation } from "@/hooks/useTranslation";
import ContactClient from './ContactClient';

export async function generateMetadata({ params }) {
  const locale = params.locale || 'fr';
  const t = getTranslation(locale, 'pages');

  return {
    title: t('contact.title'),
    description: t('contact.description'),
    openGraph: {
      title: t('contact.title'),
      description: t('contact.description'),
      images: [{ url: "https://kapreon.com/og-default.jpg" }],
      url: `https://kapreon.com/${locale}/contact`,
      locale: locale === 'fr' ? 'fr_CA' : 'en_CA',
    },
    twitter: {
      card: "summary_large_image",
      title: t('contact.title'),
      description: t('contact.description'),
      images: ["https://kapreon.com/og-default.jpg"],
    },
  };
}

export default function ContactPage({ params }) {
  return <ContactClient params={params} />;
}
