"use client";

import { useEffect, useState } from "react";
import styles from "./page.module.scss";
import Hero from "@/components/Heros/Hero";
import BasicCenterCTA from "../../../components/CallToAction/BasicCenterCTA";
import { useTranslation } from "@/hooks/useTranslation";

export default function ContactClient({ params }) {
  const [iframeAccessible, setIframeAccessible] = useState(true);
  const { t } = useTranslation('pages');
  const locale = params.locale || 'fr';

  useEffect(() => {
    const iframeUrl = "https://numigi.allo.konvergo.app/kapreon-contact";

    fetch(iframeUrl, { mode: "no-cors" })
      .then(() => {
        console.log("Konvergo Bot iFrame working.");
        setIframeAccessible(true);
      })
      .catch((error) => {
        console.error("Fallback: Konvergo Bot iFrame not working", error);
        setIframeAccessible(false);
      });
  }, []);

  return (
    <main className={styles.main}>
      <Hero 
        title={t('contact.hero_title')} 
        subtitle={t('contact.hero_subtitle')} 
        locale={locale}
      />

      {iframeAccessible ? (
        <iframe
          src="https://numigi.allo.konvergo.app/kapreon-contact"
          className={`${styles.contactBot} container`}
        />
      ) : (
        <p className="container">
          {t('contact.maintenance_message')}
        </p>
      )}

      {/* <BasicCenterCTA /> */}
    </main>
  );
}
