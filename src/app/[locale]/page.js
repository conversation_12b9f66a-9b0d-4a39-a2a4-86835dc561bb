import { getTranslation } from '@/hooks/useTranslation';
import HomeClient from './HomeClient';

export async function generateMetadata({ params }) {
  const locale = params.locale || 'fr';
  const t = getTranslation(locale, 'pages');

  return {
    title: t('home.title'),
    description: t('home.description'),
    openGraph: {
      title: t('home.title'),
      description: t('home.description'),
      images: [{ url: "https://kapreon.com/og-default.jpg" }],
      url: `https://kapreon.com${locale === 'en' ? '/en' : ''}`,
      locale: locale === 'fr' ? 'fr_CA' : 'en_CA',
    },
    twitter: {
      card: "summary_large_image",
      title: t('home.title'),
      description: t('home.description'),
      images: ["https://kapreon.com/og-default.jpg"],
    },
  };
}

export default function Home({ params }) {
  return <HomeClient params={params} />;
}
